import React from "react";
import { Form, FormikProvider, useFormik } from "formik";
import * as Yup from "yup";
import { get } from "lodash";
import { IRoom, RoomStatus } from "../../../Interface/room.interface";
import { useUpdateRoom } from "../../../server-action/API/HotelConfiguration/room";
import { FormField } from "../../BookingManagement/components/ReservationCustomForm";
import { Icon } from "@iconify/react/dist/iconify.js";
import cross from "../../../assets/Svg/Cross.svg";

interface RoomStatusEditFormProps {
  room: IRoom;
  onClose: () => void;
  onSuccess?: () => void;
}

const RoomStatusEditValidationSchema = Yup.object().shape({
  status: Yup.string().required("Status is required"),
  notes: Yup.string(),
});

const RoomStatusEditForm: React.FC<RoomStatusEditFormProps> = ({ room, onClose, onSuccess }) => {
  const updateRoomMutation = useUpdateRoom();

  // Room status can always be edited, but with some restrictions
  const canEditStatus = true;

  const formik = useFormik({
    initialValues: {
      status: room.status || RoomStatus.CLEANING,
      notes: room.notes || "",
    },
    validationSchema: RoomStatusEditValidationSchema,
    onSubmit: async (values) => {
      try {
        const roomUpdateData = {
          status: values.status as RoomStatus,
          notes: values.notes,
          ...(values.status === RoomStatus.AVAILABLE && { lastCleaned: new Date().toISOString() }),
        };

        await updateRoomMutation.mutateAsync({
          roomData: roomUpdateData,
          _id: room._id!,
        });

        onSuccess?.();
        onClose();
      } catch (error) {
        console.error("Error updating room status:", error);
      }
    },
  });

  // All available status options for dropdown
  const allStatusOptions = [
    { value: RoomStatus.AVAILABLE, label: "Available" },
    { value: RoomStatus.OCCUPIED, label: "Occupied" },
    { value: RoomStatus.CLEANING, label: "Cleaning" },
    { value: RoomStatus.MAINTENANCE, label: "Maintenance" },
    { value: RoomStatus.RESERVED, label: "Reserved" },
  ];

  // Status options - show all available statuses
  const statusOptions = allStatusOptions;

  const formFields = [
    {
      label: "Room Status",
      field: "status",
      type: "select",
      required: true,
      options: statusOptions,
      disabled: false,
      placeholder: "Select Status",
    },
    {
      label: "Notes",
      field: "notes",
      type: "textarea",
      required: false,
      placeholder: "Add any notes about the status change...",
    },
  ];

  return (
    <div className="w-full max-w-4xl mx-auto">
      <div className="bg-white rounded-lg shadow-lg">
        {/* Header */}
        <div className="flex justify-between items-center p-6 border-b border-gray-200 bg-[#EBFEF4]">
          <h2 className="text-xl font-semibold text-gray-900">
            Edit Room {room.roomNo} Status
          </h2>
          <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
            <img src={cross} alt="Close" className="w-6 h-6" />
          </button>
        </div>

        <div className="p-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Left Column - Room Information */}
            <div>
              <div className="bg-white rounded-lg shadow-sm border p-6">
                <h2 className="text-lg font-semibold text-gray-900 mb-4">Room Summary</h2>

                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">Room Number</span>
                    <span className="font-medium">{room.roomNo}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">Room Type</span>
                    <span className="font-medium">{get(room, "roomType.name", "N/A")}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">Current Status</span>
                    <span className="font-medium capitalize">{room.status}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">Floor</span>
                    <span className="font-medium">{room.floor || "N/A"}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">Last Cleaned</span>
                    <span className="font-medium">
                      {room.lastCleaned ? new Date(room.lastCleaned).toLocaleDateString() : "N/A"}
                    </span>
                  </div>

                  <div className="flex justify-between items-center pt-3 border-t-2 border-gray-300">
                    <span className="text-lg font-semibold text-gray-900">Edit Status</span>
                    <span className="text-lg font-bold text-green-600">
                      Editable
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* Right Column - Status Edit Form */}
            <div>

              <FormikProvider value={formik}>
                <Form className="space-y-4">
              {formFields.map((field) => (
                <div key={field.field}>
                  <FormField
                    label={field.label}
                    name={field.field}
                    type={field.type}
                    options={field.options}
                    required={field.required}
                    disabled={field.disabled}
                    placeholder={field.placeholder}
                    formik={formik}
                  />

                </div>
              ))}

              {/* Status Change Information */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex items-start">
                  <Icon
                    icon="mdi:information"
                    className="text-blue-600 mt-0.5 mr-2"
                    width="20"
                    height="20"
                  />
                  <div>
                    <h3 className="font-medium text-blue-800">
                      Room Status Management
                    </h3>
                    <p className="text-sm mt-1 text-blue-700">
                      You can change the room status to any available option. When changing to 'Available', the last cleaned date will be automatically updated.
                    </p>
                  </div>
                </div>
              </div>

                  {/* Action Buttons */}
                  <div className="flex justify-end gap-3 pt-4">
                    <button
                      type="button"
                      onClick={onClose}
                      className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      className="px-6 py-2 bg-[#6047E4] text-white rounded-md hover:bg-[#5038D3] disabled:opacity-50"
                      disabled={updateRoomMutation.isPending}
                    >
                      {updateRoomMutation.isPending ? "Updating..." : "Update Status"}
                    </button>
                  </div>
                </Form>
              </FormikProvider>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RoomStatusEditForm;
